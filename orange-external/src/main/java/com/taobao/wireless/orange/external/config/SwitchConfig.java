package com.taobao.wireless.orange.external.config;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;


/**
 * <AUTHOR>
 */
public class SwitchConfig {
    @AppSwitch(des = "新版控制台前端版本", level = Switch.Level.p1)
    public static String FE_VERSION = "0.0.11";

    @AppSwitch(des = "协议类型", level = Switch.Level.p1)
    public static String protocolType = "json";

    @AppSwitch(des = "combo策略", level = Switch.Level.p1)
    public static String ComboPolicy= "{\"enable\":true,\"url\":\"merge-resources.dd65024a.er.aliyun-esa.net\",\"minComboCount\":5,\"maxComboCount\":100,\"batchSize\":102400}";
}
