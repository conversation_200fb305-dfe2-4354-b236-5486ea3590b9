package com.taobao.wireless.orange.manager.config.generator;

import com.taobao.mtop.commons.utils.CollectionUtil;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.config.model.Condition;
import com.taobao.wireless.orange.manager.config.model.Expression;
import com.taobao.wireless.orange.manager.config.model.GrayConfig;
import com.taobao.wireless.orange.manager.config.model.Parameter;
import com.taobao.wireless.orange.manager.model.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class GrayConfigGenerator extends AbstractConfigGenerator<GrayConfig> {
    @Override
    protected List<String> getReleaseVersions(String namespaceId, String baseVersion) {
        return releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getNamespaceId, namespaceId)
                .in(OReleaseOrderDO::getStatus, ReleaseOrderStatus.getNotFinishedStatuses())
                .isNotNull(OReleaseOrderDO::getPercent)
                .list()
                .stream()
                .map(OReleaseOrderDO::getReleaseVersion)
                .collect(Collectors.toList());
    }

    @Override
    protected List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> releaseVersions) {
        if (CollectionUtil.isEmpty(releaseVersions)) {
            return Collections.emptyList();
        }
        return parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                .in(OParameterVersionDO::getReleaseVersion, releaseVersions)
                .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                .ne(OParameterVersionDO::getChangeType, ChangeType.DELETE)
                .list();
    }

    @Override
    protected GrayConfig buildResult(NamespaceIdNameRecord namespace, List<Parameter> parameters, List<String> offlineParameters, Map<String, OConditionVersionDO> conditionMap) {
        List<GrayConfig.ReleaseOrder> orders = buildGrayReleaseOrders(namespace.namespaceId());

        return GrayConfig.builder()
                .schemaVersion("1.0")
                .namespace(namespace.name())
                .strategy(ConfigStrategy.FULL)
                .type(ConfigType.GRAY)
                .conditions(conditionMap.values().stream()
                        .map(c -> Condition.builder()
                                .id(c.getConditionId())
                                .expression(JSON.parse(c.getExpression(), Expression.class))
                                .build())
                        // 端上依赖排序，加速检索
                        .sorted(Comparator.comparing(Condition::getId))
                        .collect(Collectors.toList()))
                .orders(orders)
                .build();
    }

    private List<GrayConfig.ReleaseOrder> buildGrayReleaseOrders(String namespaceId) {
        // 获取所有需要的发布版本号
        final List<String> releaseVersions = getReleaseVersions(namespaceId, null);

        // 批量获取所有版本的参数，减少数据库查询次数
        final Map<String, List<OParameterVersionDO>> versionToParamsMap;
        if (!releaseVersions.isEmpty()) {
            List<OParameterVersionDO> allParams = parameterVersionDAO.lambdaQuery()
                    .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                    .in(OParameterVersionDO::getReleaseVersion, releaseVersions)
                    .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                    .list();

            // 按版本号分组
            versionToParamsMap = allParams.stream()
                    .collect(Collectors.groupingBy(OParameterVersionDO::getReleaseVersion));
        } else {
            versionToParamsMap = Collections.emptyMap();
        }

        return releaseVersions.stream().map(version -> {
            final List<OParameterVersionDO> list = versionToParamsMap.getOrDefault(version, Collections.emptyList());
            if (list.isEmpty()) {
                return null;
            }

            // 分离在线和离线参数
            Map<Boolean, List<OParameterVersionDO>> partitionedParams = list.stream()
                    .collect(Collectors.partitioningBy(p -> !ChangeType.DELETE.equals(p.getChangeType())));

            final List<OParameterVersionDO> online = partitionedParams.get(true);
            final List<String> offline = partitionedParams.get(false).stream()
                    .map(OParameterVersionDO::getParameterKey)
                    .collect(Collectors.toList());

            // 批量获取参数条件
            final Map<String, List<OParameterConditionVersionDO>> paramConditionsMap;
            if (!online.isEmpty()) {
                List<String> parameterIds = online.stream()
                        .map(OParameterVersionDO::getParameterId)
                        .collect(Collectors.toList());

                List<OParameterConditionVersionDO> allConditions = parameterConditionVersionDAO.lambdaQuery()
                        .eq(OParameterConditionVersionDO::getNamespaceId, namespaceId)
                        .in(OParameterConditionVersionDO::getParameterId, parameterIds)
                        .in(OParameterConditionVersionDO::getStatus, Arrays.asList(VersionStatus.RELEASED, VersionStatus.INIT))
                        .ne(OParameterConditionVersionDO::getChangeType, ChangeType.DELETE)
                        .list();

                // 按参数ID分组
                paramConditionsMap = allConditions.stream()
                        .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId));
            } else {
                paramConditionsMap = Collections.emptyMap();
            }

            List<Parameter> params = online.stream()
                    .map(p -> {
                        List<OParameterConditionVersionDO> conditions = paramConditionsMap
                                .getOrDefault(p.getParameterId(), Collections.emptyList());

                        Map<String, OParameterConditionVersionDO> condMap = conditions.stream()
                                .collect(Collectors.toMap(
                                        OParameterConditionVersionDO::getConditionId,
                                        Function.identity(),
                                        (v1, v2) -> VersionStatus.INIT.equals(v1.getStatus()) ? v1 : v2));

                        return buildParameter(p, new ArrayList<>(condMap.values()));
                    })
                    .filter(Objects::nonNull)
                    // 端上依赖排序，加速检索
                    .sorted(Comparator.comparing(Parameter::getKey))
                    .collect(Collectors.toList());

            return GrayConfig.ReleaseOrder.builder()
                    .version(Long.parseLong(version))
                    .parameters(params)
                    .offlineParameters(offline)
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    protected List<VersionStatus> getVersionStatuses() {
        return Arrays.asList(VersionStatus.RELEASED, VersionStatus.INIT);
    }
}
