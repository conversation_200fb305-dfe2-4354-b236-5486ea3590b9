package com.taobao.wireless.orange.manager.release;

import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import lombok.Data;

/**
 * 发布发布单操作上下文
 */
@Data
public class OperationContext {
    private String releaseVersion;
    private OReleaseOrderDO releaseOrder;
    private ONamespaceDO namespace;
    private Object additionalData; // 用于传递额外的操作数据，如灰度比例等

    public OperationContext(String releaseVersion) {
        this.releaseVersion = releaseVersion;
    }

    public OperationContext(String releaseVersion, Object additionalData) {
        this.releaseVersion = releaseVersion;
        this.additionalData = additionalData;
    }
}