package com.taobao.wireless.orange.manager;

import com.taobao.mtop.commons.utils.CollectionUtil;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.OIndexDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.external.config.SwitchConfig;
import com.taobao.wireless.orange.manager.config.ConfigManager;
import com.taobao.wireless.orange.manager.config.model.Index;
import com.taobao.wireless.orange.manager.model.NamespaceIdNameRecord;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class IndexManager {
    @Autowired
    private ConfigManager configManager;

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private ResourceManager resourceManager;

    @Autowired
    private OIndexDAO indexDAO;

    @Value("${orange.cdn.domain}")
    private String cdnDomain;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;
    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    /**
     * 生成指定应用的索引文件(包含全量和差量)
     *
     * @param appKey 应用标识
     * @return 生成的索引文件列表
     */
    public List<OIndexDO> generate(String appKey, List<ONamespaceVersionDO> namespaceVersions, String indexVersion) {
        if (CollectionUtil.isEmpty(namespaceVersions)) {
            return List.of();
        }

        // 生成全量索引文件内容
        Index fullIndex = generateFullIndex(appKey, namespaceVersions, indexVersion);
        OResourceDO fullResource = resourceManager.create(fullIndex, ResourceType.FULL_INDEX);

        List<OIndexDO> newIndices = new ArrayList<>();
        OIndexDO fullIndexDO = OIndexDO.builder()
                .indexVersion(indexVersion)
                .appKey(appKey)
                .baseIndexVersion("0")
                .isAvailable(Available.Y)
                .indexResourceId(fullResource.getResourceId())
                .build();
        newIndices.add(fullIndexDO);

        // todo: 可以提取成配置
        List<String> baseIndexVersions = filterNamespaceVersionsByTimeGaps(LocalDateTime.now(), namespaceVersions, Arrays.asList(60, 120, 180));

        for (String baseIndexVersion : baseIndexVersions) {
            // 生成差量索引
            Index incrementalIndex = generateIncrementalIndex(fullIndex, Pair.of(baseIndexVersion, indexVersion));
            OResourceDO indexResource = resourceManager.create(incrementalIndex, ResourceType.INCREMENTAL_INDEX);

            OIndexDO diffIndexDO = OIndexDO.builder()
                    .indexVersion(indexVersion)
                    .appKey(appKey)
                    .baseIndexVersion(baseIndexVersion)
                    .isAvailable(Available.Y)
                    .indexResourceId(indexResource.getResourceId())
                    .build();

            newIndices.add(diffIndexDO);
        }

        // 新增索引记录
        createIndices(appKey, newIndices);

        return newIndices;
    }

    /**
     * 新增索引记录
     *
     * @param appKey
     * @param indices
     */
    private void createIndices(String appKey, List<OIndexDO> indices) {
        // 失效历史索引
        indexDAO.lambdaUpdate()
                .eq(OIndexDO::getAppKey, appKey)
                .set(OIndexDO::getIsAvailable, Available.N)
                .update();
        // 新增索引
        indexDAO.saveBatch(indices);
    }

    /**
     * 生成全量索引
     *
     * @param appKey
     * @param namespaceVersions
     * @return
     */
    private Index generateFullIndex(String appKey, List<ONamespaceVersionDO> namespaceVersions, String indexVersion) {
        List<String> namespaceIds = namespaceVersions.stream().map(ONamespaceVersionDO::getNamespaceId).collect(Collectors.toList());

        Map<String, NamespaceIdNameRecord> namespaceId2Obj = getNamespaceId2IdNamePair(namespaceIds);

        // todo: 待思考数据一致性问题
        Map<String, List<OReleaseOrderDO>> namespaceId2Orders = releaseOrderDAO.lambdaQuery()
                .in(OReleaseOrderDO::getNamespaceId, namespaceIds)
                // todo: 这个待优化，代表过滤出未结束的发布单
                .ne(OReleaseOrderDO::getStatus, ReleaseOrderStatus.CANCELED)
                .ne(OReleaseOrderDO::getStatus, ReleaseOrderStatus.RELEASED)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OReleaseOrderDO::getNamespaceId, Collectors.toList()));

        List<Index.Namespace> namespaces = namespaceVersions.stream().map(namespace -> {
            NamespaceIdNameRecord namespaceObj = namespaceId2Obj.get(namespace.getNamespaceId());

            // 生成全量正式发布配置内容
            OResourceDO releaseConfig = this.configManager.generateFullReleaseConfig(namespaceObj);
            Index.Config release = Optional.ofNullable(releaseConfig).map(r ->
                            Index.Config.builder()
                                    .resourceId(r.getResourceId())
                                    .version(Long.parseLong(namespace.getNamespaceVersion()))
                                    // todo: fixme
                                    .resourceSize(r.getData().length())
                                    .build())
                    .orElse(null);

            // 生成全量灰度发布配置内容
            List<Index.Order> orders = Optional.ofNullable(namespaceId2Orders.get(namespace.getNamespaceId()))
                    .orElse(List.of())
                    .stream()
                    .map(o -> {
                        return Index.Order.builder()
                                .version(Long.parseLong(o.getReleaseVersion()))
                                .grayRatio(o.getPercent())
                                .build();
                    }).toList();
            OResourceDO grayConfig = this.configManager.generateGrayConfig(namespaceObj);
            Index.GrayConfig gray = Optional.ofNullable(grayConfig).map(r ->
                            Index.GrayConfig.builder()
                                    .orders(orders)
                                    .resourceId(r.getResourceId())
                                    .resourceSize(r.getData().length())
                                    .build())
                    .orElse(null);

            return Index.Namespace.builder()
                    .name(namespaceObj.name())
                    .changeVersion(Long.parseLong(namespace.getNamespaceChangeVersion()))
                    .release(release)
                    .gray(gray)
                    // todo: 待实现
                    .experiment(null)
                    .build();
        }).collect(Collectors.toList());

        return Index.builder()
                .schemaVersion("1.0")
                .comboPolicy(JSON.parse(SwitchConfig.comboPolicy, Index.ComboPolicy.class))
                .cdn(cdnDomain)
                .appKey(appKey)
                .baseVersion(0L)
                .version(Long.parseLong(indexVersion))
                .offlineNamespaces(List.of())
                .strategy(ConfigStrategy.FULL)
                .namespaces(namespaces)
                .build();
    }

    private List<ONamespaceVersionDO> getAvailableNamespaceVersions(String appKey, String baseIndexVersion) {
        return namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getAppKey, appKey)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .gt(baseIndexVersion != null && !"0".equals(baseIndexVersion), ONamespaceVersionDO::getNamespaceChangeVersion, baseIndexVersion)
                .list();
    }

    /**
     * 生成差量索引文件
     *
     * @param fullIndex
     * @param changeVersionRange
     * @return
     */
    public Index generateIncrementalIndex(Index fullIndex, Pair<String, String> changeVersionRange) {
        String appKey = fullIndex.getAppKey();
        List<ONamespaceVersionDO> namespaceVersions = namespaceVersionManager.getAvailableNamespaceVersions(appKey, changeVersionRange);

        List<String> namespaceIds = namespaceVersions.stream().map(ONamespaceVersionDO::getNamespaceId).collect(Collectors.toList());
        Map<String, NamespaceIdNameRecord> namespaceId2Obj = getNamespaceId2IdNamePair(namespaceIds);

        Map<String, Index.Namespace> namespaceName2FullConfig = fullIndex.getNamespaces()
                .stream()
                .collect(Collectors.toMap(Index.Namespace::getName, Function.identity()));

        String baseIndexVersion = changeVersionRange.getLeft();

        List<Index.Namespace> namespaces = namespaceVersions.stream().map(namespace -> {
            NamespaceIdNameRecord namespaceObj = namespaceId2Obj.get(namespace.getNamespaceId());

            OResourceDO releaseConfig = this.configManager.generateIncrementalReleaseConfig(namespaceObj, baseIndexVersion);
            Index.Config release = Optional.ofNullable(releaseConfig).map(r -> Index.Config.builder()
                            .resourceId(r.getResourceId())
                            // todo: fixme
                            .resourceSize(r.getData().length())
                            .version(Long.parseLong(namespace.getNamespaceVersion()))
                            .build())
                    .orElse(null);

            Index.Namespace fullNamespaceConfig = namespaceName2FullConfig.get(namespaceObj.name());
            return Index.Namespace.builder()
                    .name(namespaceObj.name())
                    .changeVersion(Long.parseLong(namespace.getNamespaceChangeVersion()))
                    .release(release)
                    // 灰度和实验都只使用全量配置
                    .gray(fullNamespaceConfig.getGray())
                    .experiment(fullNamespaceConfig.getExperiment())
                    .build();
        }).collect(Collectors.toList());

        Index index = BeanUtil.createFromProperties(fullIndex, Index.class);
        index.setStrategy(ConfigStrategy.INCREMENTAL);
        index.setBaseVersion(Long.parseLong(baseIndexVersion));
        index.setOfflineNamespaces(getOfflineNamespaceNames(appKey, changeVersionRange));
        index.setNamespaces(namespaces);

        return index;
    }

    /**
     * 获取需要下线的命名空间名称
     *
     * @param appKey
     * @param changeVersionRange
     * @return
     */
    private List<String> getOfflineNamespaceNames(String appKey, Pair<String, String> changeVersionRange) {
        List<String> namespaceIds = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getAppKey, appKey)
                .gt(ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getLeft())
                .le(ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getRight())
                .eq(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.DELETE_NAMESPACE)
                .list()
                .stream()
                .map(ONamespaceVersionDO::getNamespaceId)
                .collect(Collectors.toList());

        return namespaceDAO.lambdaQuery()
                .select(ONamespaceDO::getName)
                .in(ONamespaceDO::getNamespaceId, namespaceIds)
                .list()
                .stream()
                .map(ONamespaceDO::getName)
                .collect(Collectors.toList());
    }

    /**
     * 获取 namespace Id 到 NamespaceIdNamePairBO 的映射
     *
     * @param namespaceIds 命名空间ID列表
     * @return namespace Id 到 NamespaceIdNamePairBO 的映射
     */
    private Map<String, NamespaceIdNameRecord> getNamespaceId2IdNamePair(List<String> namespaceIds) {
        return namespaceDAO.lambdaQuery()
                .select(ONamespaceDO::getNamespaceId, ONamespaceDO::getName)

                .in(ONamespaceDO::getNamespaceId, namespaceIds)
                .ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE)
                .list()

                .stream()
                .collect(Collectors.toMap(ONamespaceDO::getNamespaceId,
                        n -> new NamespaceIdNameRecord(n.getNamespaceId(), n.getName())));
    }

    /**
     * 根据时间间隔拆分命名空间版本
     *
     * @param now
     * @param namespaceVersions
     * @param timeGaps
     * @return
     */
    private static List<String> filterNamespaceVersionsByTimeGaps(LocalDateTime now, List<ONamespaceVersionDO> namespaceVersions, List<Integer> timeGaps) {
        // todo: 变成配置
        if (namespaceVersions.size() < 3) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();
        List<Long> leftValues = timeGaps.stream().sorted().map(timeGap -> now.getNano() - (timeGap * 60 * 1000L)).toList();
        int i = 0;
        for (Long leftValue : leftValues) {
            for (; i < namespaceVersions.size() - 1; i++) {
                ONamespaceVersionDO large = namespaceVersions.get(i);
                ONamespaceVersionDO small = namespaceVersions.get(i + 1);
                if (large.getGmtCreate().getTime() >= leftValue && small.getGmtCreate().getTime() < leftValue) {
                    result.add(large.getNamespaceChangeVersion());
                    break;
                }
            }
        }

        return result;
    }

}
