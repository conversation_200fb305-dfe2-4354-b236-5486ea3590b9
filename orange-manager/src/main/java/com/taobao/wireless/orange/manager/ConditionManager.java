package com.taobao.wireless.orange.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.ConditionStatus;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterConditionVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.model.ConditionBO;
import com.taobao.wireless.orange.manager.model.ConditionVersionBO;
import com.taobao.wireless.orange.manager.model.ParameterConditionVersionBO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ConditionManager {

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OParameterDAO parameterDAO;

    /**
     * 更新条件
     *
     * @param condition
     */
    public void update(OConditionDO condition) {
        conditionDAO.lambdaUpdate()
                .eq(OConditionDO::getConditionId, condition.getConditionId())
                .set(StringUtils.isNotBlank(condition.getName()), OConditionDO::getName, condition.getName())
                .set(StringUtils.isNotBlank(condition.getColor()), OConditionDO::getColor, condition.getColor())
                .update(condition);
    }

    /**
     * 批量创建条件变更
     *
     * @param namespace         命名空间
     * @param releaseVersion    发布版本
     * @param conditionVersions 条件版本列表
     */
    public void createConditionVersions(ONamespaceDO namespace, String releaseVersion, List<ConditionVersionBO> conditionVersions) {
        if (CollectionUtils.isEmpty(conditionVersions)) {
            return;
        }

        // 条件实体
        List<OConditionDO> newConditions = conditionVersions.stream().map(conditionVersionBO -> {
            OConditionDO condition = conditionVersionBO.getCondition();

            // 说明是新增的条件
            if (StringUtils.isBlank(condition.getConditionId())) {
                String conditionId = SerializeUtil.UUID();
                // 为入参对象填充 conditionId
                conditionVersionBO.setConditionId(conditionId);
                condition.setAppKey(namespace.getAppKey());
                condition.setNamespaceId(namespace.getNamespaceId());
                condition.setStatus(ConditionStatus.INIT);
                condition.setConditionId(conditionId);
            }

            return condition;
        }).collect(Collectors.toList());

        // 校验条件名称是否重复
        checkConditionNameDuplicate(namespace.getNamespaceId(), newConditions);

        List<String> conditionIds = conditionVersions.stream()
                .map(ConditionVersionBO::getConditionId)
                .collect(Collectors.toList());

        var onlineConditionId2Versions = getOnlineConditionVersionMap(conditionIds);

        // 创建条件版本
        List<OConditionVersionDO> newConditionVersions = conditionVersions.stream().map(conditionVersionBO -> {
            OConditionDO condition = conditionVersionBO.getCondition();

            conditionVersionBO.setStatus(VersionStatus.INIT);
            conditionVersionBO.setAppKey(namespace.getAppKey());
            conditionVersionBO.setNamespaceId(namespace.getNamespaceId());
            conditionVersionBO.setReleaseVersion(releaseVersion);
            OConditionVersionDO onlineConditionVersion = onlineConditionId2Versions.get(condition.getConditionId());

            // 如果不一致，代表已经有人对该条件提前提交了变更
            if (onlineConditionVersion != null && !onlineConditionVersion.getReleaseVersion().equals(conditionVersionBO.getPreviousReleaseVersion())) {
                throw CommonException.getDynamicException(ExceptionEnum.CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH, condition.getName());
            }
            return conditionVersionBO;
        }).collect(Collectors.toList());

        // 创建条件实体
        conditionDAO.saveOrUpdateBatch(newConditions);
        // 创建条件版本
        conditionVersionDAO.saveBatch(newConditionVersions);
    }

    private void checkConditionNameDuplicate(String namespaceId, List<OConditionDO> newConditions) {
        List<String> conditionName = newConditions.stream().map(OConditionDO::getName).collect(Collectors.toList());
        var duplicateConditions = conditionDAO.getByNames(namespaceId, conditionName);
        if (CollectionUtils.isNotEmpty(duplicateConditions)) {
            throw CommonException.getDynamicException(ExceptionEnum.CONDITION_NAME_DUPLICATE, duplicateConditions.stream().map(OConditionDO::getName).collect(Collectors.joining()));
        }
    }

    /**
     * 查询条件列表，支持分页和条件查询
     *
     * @param query 查询条件
     * @return 条件分页列表结果
     */
    public Page<ConditionBO> query(ConditionBO query, Pagination pagination) {
        var pageResult = conditionDAO.lambdaQuery()
                .eq(OConditionDO::getNamespaceId, query.getNamespaceId())
                .ne(OConditionDO::getStatus, ConditionStatus.INVALID)
                .like(StringUtils.isNotBlank(query.getName()), OConditionDO::getName, query.getName())
                .orderByDesc(OConditionDO::getId)
                .page(PageUtil.build(pagination));

        Page<ConditionBO> result = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());

        List<OConditionDO> conditions = pageResult.getRecords();
        if (CollectionUtils.isEmpty(conditions)) {
            result.setRecords(new ArrayList<>());
            return result;
        }

        List<String> conditionIds = conditions.stream().map(OConditionDO::getConditionId).collect(Collectors.toList());
        var conditionId2ParamCondition = getRelatedParameters(conditionIds);
        var conditionId2ConditionVersion = getOnlineFirstConditionVersionsMap(conditionIds);

        List<ConditionBO> conditionVersions = conditions
                .stream()
                .map(c -> {
                    ConditionBO condition = BeanUtil.createFromProperties(c, ConditionBO.class);
                    condition.setConditionVersion(conditionId2ConditionVersion.get(c.getConditionId()));
                    condition.setRelatedParameters(conditionId2ParamCondition.get(c.getConditionId()));
                    return condition;
                })
                .collect(Collectors.toList());
        result.setRecords(conditionVersions);
        return result;
    }

    /**
     * 获取指定的所有条件
     *
     * @param query
     * @return
     */
    public List<ConditionBO> getAllOnlineConditions(ConditionBO query) {
        var conditionId2ConditionVersion = conditionVersionDAO.lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getNamespaceId()), OConditionVersionDO::getNamespaceId, query.getNamespaceId())
                .eq(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .ne(OConditionVersionDO::getChangeType, ChangeType.DELETE)
                .list()
                .stream()
                .collect(Collectors.toMap(OConditionVersionDO::getConditionId, Function.identity()));

        return conditionDAO.lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getNamespaceId()), OConditionDO::getNamespaceId, query.getNamespaceId())
                .eq(OConditionDO::getStatus, ConditionStatus.ONLINE)
                .list()
                .stream()
                .map(c -> {
                    ConditionBO condition = BeanUtil.createFromProperties(c, ConditionBO.class);
                    condition.setConditionVersion(conditionId2ConditionVersion.get(c.getConditionId()));
                    return condition;
                })
                .toList();
    }

    public ConditionBO getOnlineConditionDetailByConditionId(String conditionId) {
        OConditionDO condition = conditionDAO.getByConditionId(conditionId);
        if (condition == null) {
            throw new CommonException(ExceptionEnum.CONDITION_NOT_FOUND);
        }

        OConditionVersionDO conditionVersion = getOnlineConditionVersionMap(List.of(conditionId)).get(conditionId);

        if (conditionVersion == null) {
            throw new CommonException(ExceptionEnum.CONDITION_NOT_FOUND);
        }

        var conditionBO = BeanUtil.createFromProperties(condition, ConditionBO.class);
        conditionBO.setConditionVersion(conditionVersion);
        return conditionBO;
    }

    /**
     * 获取条件对应的在线版本
     *
     * @param conditionIds 条件ID列表
     * @return 条件ID与条件版本的映射
     */
    public Map<String, OConditionVersionDO> getOnlineConditionVersionMap(List<String> conditionIds) {
        return getConditionVersions(conditionIds, List.of(VersionStatus.RELEASED))
                .stream()
                .collect(Collectors.toMap(OConditionVersionDO::getConditionId, Function.identity()));
    }

    private Map<String, OConditionVersionDO> getOnlineFirstConditionVersionsMap(List<String> conditionIds) {
        return getConditionVersions(conditionIds, Arrays.asList(VersionStatus.RELEASED, VersionStatus.INIT))
                .stream()
                .collect(Collectors.toMap(
                        OConditionVersionDO::getConditionId,
                        Function.identity(),
                        (v1, v2) -> {
                            return v1.getStatus().equals(VersionStatus.RELEASED) ? v1 : v2;
                        }
                ));
    }

    private List<OConditionVersionDO> getConditionVersions(List<String> conditionIds, List<VersionStatus> statuses) {
        if (CollectionUtils.isEmpty(conditionIds)) {
            return List.of();
        }

        return conditionVersionDAO.lambdaQuery()
                .in(OConditionVersionDO::getConditionId, conditionIds)
                .ne(OConditionVersionDO::getChangeType, ChangeType.DELETE)
                .in(CollectionUtils.isNotEmpty(statuses), OConditionVersionDO::getStatus, statuses)
                .list();
    }

    /**
     * 获取条件被引用的参数列表
     *
     * @param conditionIds
     * @return
     */
    private Map<String, List<ParameterConditionVersionBO>> getRelatedParameters(List<String> conditionIds) {
        if (CollectionUtils.isEmpty(conditionIds)) {
            return new HashMap<>();
        }

        var conditionId2ParamCondition = parameterConditionVersionDAO.lambdaQuery()
                .in(OParameterConditionVersionDO::getConditionId, conditionIds)
                // status 是 init 或者 (status 是 released 但是 changeType 不是 delete）
                .and(q -> q.eq(OParameterConditionVersionDO::getStatus, VersionStatus.INIT)
                        .or(subQ -> subQ.eq(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED)
                                .ne(OParameterConditionVersionDO::getChangeType, ChangeType.DELETE)))
                .list()
                .stream()
                .map(i -> BeanUtil.createFromProperties(i, ParameterConditionVersionBO.class))
                .collect(Collectors.groupingBy(ParameterConditionVersionBO::getConditionId));

        // 填充参数名称
        setParameterKey(conditionId2ParamCondition);

        return conditionId2ParamCondition;
    }

    private void setParameterKey(Map<String, List<ParameterConditionVersionBO>> conditionId2ParamCondition) {
        List<String> parameterIds = conditionId2ParamCondition.values().stream()
                .flatMap(Collection::stream)
                .map(ParameterConditionVersionBO::getParameterId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, OParameterDO> parameterId2ByParameter = parameterDAO.getParameterMapByParameterIds(parameterIds);

        conditionId2ParamCondition.forEach((k, v) -> {
            v.forEach(i ->
                    i.setParameterKey(parameterId2ByParameter.get(i.getParameterId()).getParameterKey()));
        });
    }
}
