package com.taobao.wireless.orange.manager;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.model.proto.IndexProto;
import com.taobao.wireless.orange.dal.enhanced.dao.OResourceDAO;
import com.taobao.wireless.orange.external.config.SwitchConfig;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Base64;


public class ProbeManagerTest extends BaseIntegrationTest {
    @Autowired
    private ProbeManager probeManager;

    @Autowired
    private OResourceDAO resourceDAO;

    @Test
    public void generate() {
        SwitchConfig.protocolType = "protobuf";
        var newProbes = probeManager.generate();
        System.out.println(newProbes);
    }

    public static void main(String[] args) throws InvalidProtocolBufferException {
        var data = "CgMxLjASM28tY29uZmlnLXRlc3Rpbmcub3NzLXJnLWNoaW5hLW1haW5sYW5kLmFsaXl1bmNzLmNvbRoIMjEzODA3OTAgqNOGl8LFnY0cQk0KDOmYruiQpOa1i+ivlRC8io7rgrSbjRwiMwokYjBjMGY0ZWM2MTk4NDdhZTgwODU0OWU5NTUwZjAwZTIuYmluELyKjuuCtJuNHBj1BEJQCg9vcmFuZ2VfdGVzdF9pb3MQoLPZ8LLFnY0cIjMKJGI4NmZiNTNkZDNiMzQ3MjhhNWI3ZTM3N2ExODY1NjBjLmJpbhCgs9nwssWdjRwY1FBCpgEKDXJ1YW55aW5nX3Rlc3QQqNOGl8LFnY0cIjMKJDhiZjI0MjY3YjU4NzRjZWRhMDYwYjlmNGNiM2UzYjE3LmJpbhCwx5SIwsWdjRwY1QsyVgoMCITxytaMxZ2NHBABEiQxMTgwMjgwMTYyNzA0N2U3YjEyMWM2OGM0MTVmYmUzOS5iaW4aIGMwZmNiZmI1YjRlYWNiZDE4OTdjN2IzZjdjOTM4OWJmSjYIARIqbWVyZ2UtcmVzb3VyY2VzLmRkNjUwMjRhLmVyLmFsaXl1bi1lc2EubmV0GAUgZCiAoAY=";
        byte[] byteArray = Base64.getDecoder().decode(data);
        MessageOrBuilder proto = IndexProto.parseFrom(byteArray);
        String jsonStr = JsonFormat.printer()
                .includingDefaultValueFields()
                .print(proto);
        System.out.println(jsonStr);
    }
}
