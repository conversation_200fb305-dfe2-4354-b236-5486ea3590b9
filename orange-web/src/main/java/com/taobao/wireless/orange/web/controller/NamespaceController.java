package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.NamespaceService;
import com.taobao.wireless.orange.service.model.*;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 命名空间管理控制器
 *
 * <AUTHOR>
 * @date 2025/4/14
 */
@Api(tags = "命名空间管理接口")
@RestController
@RequestMapping("/api/namespaces")
public class NamespaceController {
    /**
     * 命名空间服务接口
     */
    @Autowired
    private NamespaceService namespaceService;

    /**
     * 获取所有命名空间列表
     *
     * @return Result对象，包含命名空间列表数据
     */
    @GetMapping
    public PaginationResult<NamespaceDTO> query(NamespaceQueryDTO namespaceQueryDTO,
                                                @RequestParam(defaultValue = "1") Integer page,
                                                @RequestParam(defaultValue = "10") Integer size) {
        return namespaceService.query(namespaceQueryDTO, new Pagination(page, size));
    }

    /**
     * 创建新的命名空间
     *
     * @param namespace 需要创建的命名空间对象
     * @return Result对象，包含新创建的命名空间ID
     */
    @PostMapping
    public Result<String> create(@RequestBody NamespaceCreateDTO namespace) {
        return namespaceService.create(namespace);
    }

    /**
     * 根据ID获取指定命名空间
     *
     * @param namespaceId 命名空间ID
     * @return Result对象，包含查询到的命名空间信息
     */
    @GetMapping("/{namespaceId}")
    public Result<NamespaceDTO> getByNamespaceId(@PathVariable("namespaceId") String namespaceId) {
        return namespaceService.getByNamespaceId(namespaceId);
    }

    /**
     * 更新指定ID的命名空间信息
     *
     * @param namespaceId      待更新的命名空间ID
     * @param updatedNamespace 更新后的命名空间对象
     * @return Result对象，包含更新后的命名空间信息
     */
    @PutMapping("/{namespaceId}")
    public Result<Void> update(@PathVariable("namespaceId") String namespaceId, @RequestBody NamespaceUpdateDTO updatedNamespace) {
        updatedNamespace.setNamespaceId(namespaceId);
        return namespaceService.update(updatedNamespace);
    }

    /**
     * 删除指定ID的命名空间
     *
     * @param namespaceId
     * @return
     */
    @DeleteMapping("/{namespaceId}")
    public Result<Void> delete(@PathVariable("namespaceId") String namespaceId) {
        return namespaceService.delete(namespaceId);
    }

    /**
     * 获取指定命名空间的历史版本
     * @param namespaceId
     * @param page
     * @param size
     * @return
     */
    @GetMapping("/{namespaceId}/histories")
    public PaginationResult<NamespaceVersionDTO> histories(@PathVariable("namespaceId") String namespaceId,
                                                           @RequestParam(defaultValue = "1") Integer page,
                                                           @RequestParam(defaultValue = "10") Integer size) {
        return namespaceService.histories(namespaceId, new Pagination(page, size));
    }
}
