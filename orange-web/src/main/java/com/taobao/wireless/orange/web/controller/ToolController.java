package com.taobao.wireless.orange.web.controller;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.model.proto.GrayConfigProto;
import com.taobao.wireless.orange.common.model.proto.IndexProto;
import com.taobao.wireless.orange.common.model.proto.ReleaseConfigProto;
import com.taobao.wireless.orange.dal.enhanced.dao.OProbeDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OProbeDO;
import com.taobao.wireless.orange.external.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "工具接口")
@RestController
@RequestMapping("/api/tools")  // 复数形式
@Slf4j
public class ToolController {

    @Autowired
    private OProbeDAO probeDAO;

    @Autowired
    private OssService ossService;

    @Value("${orange.oss.bucketName}")
    private String bucketName;

    @ApiOperation("查看索引信息")
    @GetMapping("/{appKey}")
    public Result<String> generate(@PathVariable("appKey") String appKey) {
        OProbeDO one = probeDAO.lambdaQuery().eq(OProbeDO::getAppKey, appKey).eq(OProbeDO::getIsAvailable, Available.Y).one();
        return Result.success(JSON.toJSONString(one));
    }

    @ApiOperation("查看配置文件内容")
    @GetMapping("/resources/{type}/{resourceId}")
    public Result<Object> getResourceContent(@PathVariable("type") String type, @PathVariable("resourceId") String resourceId) {
        byte[] bytes = ossService.readData(bucketName, resourceId);
        try {
            MessageOrBuilder proto = null;
            if ("index".equals(type)) {
                proto = IndexProto.parseFrom(bytes);
            }
            if ("release-config".equals(type)) {
                proto = ReleaseConfigProto.parseFrom(bytes);
            }
            if ("gray-config".equals(type)) {
                proto = GrayConfigProto.parseFrom(bytes);
            }

            if (proto != null) {
                String jsonStr = JsonFormat.printer()
                        .includingDefaultValueFields()
                        .print(proto);

                return Result.success(JSON.parse(jsonStr));
            }

            return Result.success(null);
        } catch (Exception e) {
            log.error("getConfigContent index deserialize Exception", e);

            // 尝试直接将内容返回
            Result<Object> result = new Result<Object>();
            result.setData(new String(bytes));
            result.setSuccess(false);
            result.setMessage(ExceptionEnum.RESOURCE_DESERIALIZE_ERROR.getMessage());
            result.setCode(ExceptionEnum.RESOURCE_DESERIALIZE_ERROR.getCode());
            return result;
        }
    }
}
